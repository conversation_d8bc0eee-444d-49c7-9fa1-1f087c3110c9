using Microsoft.Extensions.Hosting;
using OpenIddict.Abstractions;
using static OpenIddict.Abstractions.OpenIddictConstants;
using System.Threading.Tasks;
using System.Threading;
using System;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using Triquestra.InfinityAPI.OpenIddict.Infrastructure;
using OpenIddict.EntityFrameworkCore.Models;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;

namespace Triquestra.InfinityAPI.OpenIddictApi
{
    public class Worker : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;

        public Worker(IServiceProvider serviceProvider)
            => _serviceProvider = serviceProvider;

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            using var scope = _serviceProvider.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<OpenIddictDbContext>();
            await context.Database.EnsureCreatedAsync();
            var clientRepo = scope.ServiceProvider.GetRequiredService<IClientRepository>();

            var manager = scope.ServiceProvider.GetRequiredService<IOpenIddictApplicationManager>();
            // create test clients for testing
            await CreateClient(manager, clientRepo, "rw", "ReadWrite");
            await CreateClient(manager, clientRepo, "ro", "Read");

        }



        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;


        private async Task CreateClient(IOpenIddictApplicationManager manager, IClientRepository clientRepository, string clientId, params string[] scopes)
        {
            var c = await manager.FindByClientIdAsync(clientId) as Client;
            if (c is null)
            {
                var id = new OpenIddictApplicationDescriptor
                {
                    ClientId = clientId,
                    ClientSecret = "p@ssword",
                    Permissions =
                        {
                            Permissions.Endpoints.Token,
                            Permissions.GrantTypes.ClientCredentials
                       },
                    DisplayName = clientId
                };
                id.SetAccessTokenLifetime(TimeSpan.FromHours(8));

                await manager.CreateAsync(id);

                var client = clientRepository.GetClientByClientId(id.ClientId);
                foreach (var s in scopes)
                {
                    client.Scopes.Add(new ClientScope
                    {
                        Client = client,
                        ClientId = client.Id,
                        Name = s
                    });
                }

                client.Claims.Add(new ClientClaim
                {
                    Client = client,
                    ClientId = client.Id,
                    Type = "terminalId",
                    Value = "1"
                });
                client.Claims.Add(new ClientClaim
                {
                    Client = client,
                    ClientId = client.Id,
                    Type = "site_code",
                    Value = "99"
                });
                await clientRepository.UnitOfWork.SaveChangesAsync();
            }
        }
    }
}
