using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using Triquestra.Common.Logging.Nlog;
using Triquestra.InfinityApi.Common.Logging.Middleware;
using Triquestra.InfinityAPI.Common.API;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;
using Triquestra.InfinityAPI.OpenIddict.Infrastructure;
using Triquestra.InfinityAPI.OpenIddictApi.AutofacModules;
using Triquestra.InfinityAPI.OpenIddictApi.Options;

namespace Triquestra.InfinityAPI.OpenIddictApi
{
    public class Startup : BaseStartup
    {
        private string API_VERSION = "";
        private ApiConfiguration _apiCfg;

        public Startup(IWebHostEnvironment env, ILoggerFactory loggerFactory) : base(env, loggerFactory)
        {
            _apiCfg = readConfiguration();
            var assembly = Assembly.GetExecutingAssembly();
            var fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            API_VERSION = fvi.FileVersion;
        }

        public override IServiceProvider ConfigureServices(IServiceCollection services)
        {
            var connectionString = _apiCfg.ConnectionString;

            services.AddResponseCompression();
            var migrationsAssembly = typeof(Startup).Assembly.FullName;
           
            services.AddLogging();
            services.AddLogging(m => m.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace));

            services.AddDbContext<OpenIddictDbContext>(options =>
            {
                options.UseSqlServer(_apiCfg.ConnectionString);
                options.UseOpenIddict<Client,ApplicationAuthorization,ApplicationScope,ApplicationToken,int>();
            });

            services.AddOpenIddict()
                .AddCore(options =>
                {
                    options.UseEntityFrameworkCore()
                           .UseDbContext<OpenIddictDbContext>()
                           .ReplaceDefaultEntities<Client, ApplicationAuthorization,ApplicationScope,ApplicationToken,int>();
                }).AddServer(options =>
                {
                    options.SetTokenEndpointUris("connect/token");
                    options.AllowPasswordFlow();
                    options.AllowClientCredentialsFlow();
                    options.AcceptAnonymousClients();
                    options.DisableAccessTokenEncryption();
                    //issuer must be a valid url, the old  iss "InfinityUnifiedCommercePlatform" is no longer working.
                    //options.SetIssuer(string.IsNullOrEmpty(Configuration["issuer"]) ? "InfinityUnifiedCommercePlatform" : Configuration["issuer"]);
                    options.AddEncryptionCertificate(_apiCfg.Certificate_Encryption).AddSigningCertificate(_apiCfg.Certificate_Sign);
                    options.UseAspNetCore().DisableTransportSecurityRequirement()
                           .EnableTokenEndpointPassthrough();
                    options.DisableTokenStorage();
                }).AddValidation(options =>
                {
                    options.UseLocalServer();
                    options.UseAspNetCore();
                });
            services.AddHostedService<Worker>();
            return base.ConfigureServices(services);

        }

        public override void AddServicesCustom()
        {
        }

        public override void RegisterCustom()
        {
            CustomRegistrations.RegisterCustomClasses(ContainerReg, _apiCfg.ConnectionString);
        }

        public override void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory, IHostApplicationLifetime lifetime)
        {
            app.AddInfLog();
            app.UseStaticFiles();
            app.UseResponseCompression();
            app.UseCors("CorsPolicy");
            app.UseMiddleware<LogResponseMiddleware>();
            app.UseMiddleware<LogRequestMiddleware>();

            if (!string.IsNullOrEmpty(_apiCfg.NlogConfig))
            {
                var nlogContent = _apiCfg.NlogConfig.Replace("${api-name}", "IdentityAPI")
                    .Replace("${api-version}", API_VERSION);
                var xmlStream = new System.IO.StringReader(nlogContent);
                var xmlReader = System.Xml.XmlReader.Create(xmlStream);
                NLog.LogManager.Configuration = new NLog.Config.XmlLoggingConfiguration(xmlReader, null);
            }
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(options =>
            {
                options.MapControllers();
                options.MapDefaultControllerRoute();
            });
        }


        private X509Certificate2 LoadCertificateFromBase64(string certBase64, string pass)
        {
            byte[] certByte = Convert.FromBase64String(certBase64);
            return new X509Certificate2(certByte, pass);
        }
        private X509Certificate2 LoadCertificate(string certFile, string pass)
        {
            return new X509Certificate2(certFile, pass, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable);
        }

        private ApiConfiguration readConfiguration()
        {
            var cfg = new ApiConfiguration();
            //load the default setting
            cfg.ConnectionString = Configuration["ConnectionString"];
            cfg.InfinityIdentityCertificate_Sign = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "sign.pfx");
            cfg.InfinityIdentityCertificate_Encryption = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "encryption.pfx");
            cfg.InfinityIdentityPassword = Configuration["INFINITY_IDENTITY_PASSWORD"];

            cfg.NlogConfig = "";
            //load settings from environment variables
            if (!string.IsNullOrWhiteSpace(Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING", EnvironmentVariableTarget.Process)))
                cfg.ConnectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING", EnvironmentVariableTarget.Process);

            if (!string.IsNullOrWhiteSpace(Environment.GetEnvironmentVariable("NLOG_CONFIGURATION", EnvironmentVariableTarget.Process)))
            {
                string nlogConfigContent = Environment.GetEnvironmentVariable("NLOG_CONFIGURATION", EnvironmentVariableTarget.Process);
                cfg.NlogConfig = nlogConfigContent;
            }
            if (!string.IsNullOrWhiteSpace(Environment.GetEnvironmentVariable("INFINITY_IDENTITY_CERTIFICATE", EnvironmentVariableTarget.Process))
                && !string.IsNullOrWhiteSpace(Environment.GetEnvironmentVariable("INFINITY_IDENTITY_PASSWORD", EnvironmentVariableTarget.Process)))
            {
                cfg.InfinityIdentityCertificate_Sign = Environment.GetEnvironmentVariable("INFINITY_IDENTITY_CERTIFICATE_SIGN", EnvironmentVariableTarget.Process);
                cfg.InfinityIdentityCertificate_Encryption = Environment.GetEnvironmentVariable("INFINITY_IDENTITY_CERTIFICATE_ENCRYPTION", EnvironmentVariableTarget.Process);
                cfg.InfinityIdentityPassword = Environment.GetEnvironmentVariable("INFINITY_IDENTITY_PASSWORD", EnvironmentVariableTarget.Process);
                cfg.Certificate_Sign = LoadCertificateFromBase64(cfg.InfinityIdentityCertificate_Sign, cfg.InfinityIdentityPassword);
                cfg.Certificate_Encryption = LoadCertificateFromBase64(cfg.InfinityIdentityCertificate_Encryption, cfg.InfinityIdentityPassword);

            }
            else
            {
                cfg.Certificate_Sign = LoadCertificate(cfg.InfinityIdentityCertificate_Sign, cfg.InfinityIdentityPassword);
                cfg.Certificate_Encryption = LoadCertificate(cfg.InfinityIdentityCertificate_Encryption, cfg.InfinityIdentityPassword);
            }
            cfg.ConnectionString = AppendAppNameOfConnectionString(cfg.ConnectionString, Configuration["ApplicationName"]);
            return cfg;
        }
    }
}
