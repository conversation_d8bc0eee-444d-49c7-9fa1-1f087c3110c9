﻿using System.Security.Cryptography.X509Certificates;

namespace Triquestra.InfinityAPI.OpenIddictApi.Options
{
    public class ApiConfiguration
    {
        /// <summary>
        /// Connection string
        /// </summary>
        public string ConnectionString { get; set; }
        /// <summary>
        /// Identity Sign Certificate file path 
        /// </summary>
        public string InfinityIdentityCertificate_Sign { get; set; }

        /// <summary>
        /// Identity Encryption Certificate file path 
        /// </summary>
        public string InfinityIdentityCertificate_Encryption { get; set; }

        /// <summary>
        /// Identity Certificate file password
        /// </summary>
        public string InfinityIdentityPassword { get; set; }
        /// <summary>
        /// nlog config
        /// </summary>
        public string NlogConfig { get; set; }
        /// <summary>
        /// Certificate for sign
        /// </summary>
        public X509Certificate2 Certificate_Sign { get; set; }

        /// <summary>
        /// Certificate for encryption
        /// </summary>
        public X509Certificate2 Certificate_Encryption { get; set; }
    }
}
