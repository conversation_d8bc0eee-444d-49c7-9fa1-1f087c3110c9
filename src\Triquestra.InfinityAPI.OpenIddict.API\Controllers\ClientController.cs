using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using OpenIddict.Abstractions;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;

namespace Triquestra.InfinityAPI.OpenIddictApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(GroupName = "v1")]
    [AllowAnonymous]
    public class ClientController : ControllerBase
    {
        private readonly IOpenIddictApplicationManager _applicationManager;
        private readonly IClientRepository _clientRepository;

        public ClientController(
            IOpenIddictApplicationManager applicationManager,
            IClientRepository clientRepository)
        {
            _applicationManager = applicationManager;
            _clientRepository = clientRepository;
        }

        [HttpGet]
        public async Task<ActionResult<List<object>>> GetClients()
        {
            var result = new List<object>();
            await foreach (var application in _applicationManager.ListAsync())
            {
                result.Add(new 
                { 
                    ClientId = await _applicationManager.GetClientIdAsync(application),
                    DisplayName = await _applicationManager.GetDisplayNameAsync(application)
                });
            }
            return Ok(result);
        }

        [HttpGet("{clientId}")]
        public ActionResult<Client> GetClient(string clientId)
        {
            var client = _clientRepository.GetClientByClientId(clientId);
            if (client == null)
            {
                return NotFound();
            }

            return Ok(client);
        }

        [HttpPost]
        public async Task<ActionResult<Client>> CreateClient([FromBody] ClientCreateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var descriptor = new OpenIddictApplicationDescriptor
            {
                ClientId = request.ClientId,
                ClientSecret = request.ClientSecret,
                DisplayName = request.DisplayName,
                Permissions =
                {
                    OpenIddictConstants.Permissions.Endpoints.Token,
                    OpenIddictConstants.Permissions.GrantTypes.ClientCredentials
                }
            };

            descriptor.SetAccessTokenLifetime(TimeSpan.FromHours(8));

            // Create the OpenIddict application
            await _applicationManager.CreateAsync(descriptor);

            // Create our extended client
            var client = _clientRepository.GetClientByClientId(descriptor.ClientId);

            // Add scopes
            if (request.Scopes != null)
            {
                foreach (var scope in request.Scopes)
                {
                    client.Scopes.Add(new ClientScope
                    {
                        Client = client,
                        ClientId = client.Id,
                        Name = scope
                    });
                }
            }

            // Add claims
            if (request.Claims != null)
            {
                foreach (var claim in request.Claims)
                {
                    client.Claims.Add(new ClientClaim
                    {
                        Client = client,
                        ClientId = client.Id,
                        Type = claim.Type,
                        Value = claim.Value
                    });
                }
            }

            await _clientRepository.UnitOfWork.SaveChangesAsync();

            return CreatedAtAction(nameof(GetClient), new { clientId = client.ClientId }, client);
        }

        [HttpPut("{clientId}")]
        public async Task<IActionResult> UpdateClient(string clientId, [FromBody] ClientUpdateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var client = _clientRepository.GetClientByClientId(clientId);
            if (client == null)
            {
                return NotFound();
            }

            // Update OpenIddict application
            var application = await _applicationManager.FindByClientIdAsync(clientId);
            if (application == null)
            {
                return NotFound();
            }

            var descriptor = new OpenIddictApplicationDescriptor
            {
                ClientId = clientId,
                ClientSecret = request.ClientSecret,
                DisplayName = request.DisplayName,
                Permissions =
                {
                    OpenIddictConstants.Permissions.Endpoints.Token,
                    OpenIddictConstants.Permissions.GrantTypes.ClientCredentials
                }
            };

            await _applicationManager.UpdateAsync(application, descriptor);

            // Update scopes
            client.Scopes.Clear();
            if (request.Scopes != null)
            {
                foreach (var scope in request.Scopes)
                {
                    client.Scopes.Add(new ClientScope
                    {
                        Client = client,
                        ClientId = client.Id,
                        Name = scope
                    });
                }
            }

            // Update claims
            client.Claims.Clear();
            if (request.Claims != null)
            {
                foreach (var claim in request.Claims)
                {
                    client.Claims.Add(new ClientClaim
                    {
                        Client = client,
                        ClientId = client.Id,
                        Type = claim.Type,
                        Value = claim.Value
                    });
                }
            }

            await _clientRepository.UnitOfWork.SaveChangesAsync();

            return NoContent();
        }

        //[HttpDelete("{clientId}")]
        //public async Task<IActionResult> DeleteClient(string clientId)
        //{
        //    var client = _clientRepository.GetClientByClientId(clientId);
        //    if (client == null)
        //    {
        //        return NotFound();
        //    }

        //    // Delete from OpenIddict
        //    var application = await _applicationManager.FindByClientIdAsync(clientId);
        //    if (application != null)
        //    {
        //        await _applicationManager.DeleteAsync(application);
        //    }

        //    // Delete our extended client
        //    _clientRepository.Delete(client);
        //    await _clientRepository.UnitOfWork.SaveChangesAsync();

        //    return NoContent();
        //}
    }

    public class ClientCreateRequest
    {
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string DisplayName { get; set; }
        public List<string> Scopes { get; set; }
        public List<ClientClaimRequest> Claims { get; set; }
    }

    public class ClientUpdateRequest
    {
        public string ClientSecret { get; set; }
        public string DisplayName { get; set; }
        public List<string> Scopes { get; set; }
        public List<ClientClaimRequest> Claims { get; set; }
    }

    public class ClientClaimRequest
    {
        public string Type { get; set; }
        public string Value { get; set; }
    }
}
