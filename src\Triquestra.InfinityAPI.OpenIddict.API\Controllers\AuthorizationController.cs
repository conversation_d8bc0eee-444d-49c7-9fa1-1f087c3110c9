using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;
using static OpenIddict.Abstractions.OpenIddictConstants;
using System.Security.Claims;
using System.Threading.Tasks;
using System;
using Microsoft.AspNetCore;
using System.Collections.Immutable;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;
using System.Linq;
using static System.Net.Mime.MediaTypeNames;

namespace Triquestra.InfinityAPI.OpenIddictApi.Controllers
{
    public class AuthorizationController : ControllerBase
    {
        private readonly IOpenIddictApplicationManager _applicationManager;
        private readonly IClientRepository _clientRepository;

        public AuthorizationController(IOpenIddictApplicationManager applicationManager,
            IClientRepository clientRepository)
        { 
             _applicationManager = applicationManager;
            _clientRepository = clientRepository;
        }

        [HttpPost("~/connect/token"), Produces("application/json")]
        public async Task<IActionResult> Exchange()
        {
            var request = HttpContext.GetOpenIddictServerRequest();
            if (request.IsClientCredentialsGrantType())
            {
                // Note: the client credentials are automatically validated by OpenIddict:
                // if client_id or client_secret are invalid, this action won't be invoked.

                var client = _clientRepository.GetClientByClientId(request.ClientId);

                // Create a new ClaimsIdentity containing the claims that
                // will be used to create an id_token, a token or a code.
                var identity = new ClaimsIdentity(TokenValidationParameters.DefaultAuthenticationType, Claims.Name, Claims.Role);
                identity.SetClaim(Claims.Subject, client.ClientId);
               // identity.SetClaim(Claims.Name, client.DisplayName);
                if (client.Scopes != null && client.Scopes.Count > 0)
                {
                    foreach (var scope in client.Scopes)
                    {
                        var scopes= client.Scopes.Select(x=>x.Name).ToImmutableArray();
                        identity.SetClaims(Claims.Scope, scopes);
                    }
                }

                if (client.Claims != null && client.Claims.Count > 0)
                {
                    var claimGroups=client.Claims.GroupBy(x=>x.Type).ToImmutableDictionary(
                        g => g.Key,
                        g => g.Select(x => x.Value).ToImmutableArray());

                    foreach(var kv in claimGroups)
                    {
                        identity.SetClaims(kv.Key, kv.Value);
                    }
                }

                foreach (Claim claim in identity.Claims)
                {
                    claim.SetDestinations(OpenIddictConstants.Destinations.AccessToken, OpenIddictConstants.Destinations.IdentityToken);
                }
                var cp = new ClaimsPrincipal(identity);
                return SignIn(new ClaimsPrincipal(identity), OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
            }

            throw new NotImplementedException("The specified grant is not implemented.");
        }
    }
}
