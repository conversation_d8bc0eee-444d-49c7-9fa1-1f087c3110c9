﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>InfinityAPI.Utilities.APIDB</RootNamespace>
    <AssemblyName>InfinityAPI_MSSQL_OpenIddict</AssemblyName>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="InfinityAPI.DBManager.deps.json" />
    <None Remove="InfinityAPI.DBManager.runtimeconfig.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="001-OpenIddictV1.0.0.sql" />
    <EmbeddedResource Include="SchemaName" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="InfinityAPI.DBManager.deps.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="InfinityAPI.DBManager.runtimeconfig.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="NLog.Config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>  
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="InfinityAPI.DBManager">
      <Version>8.0.0.1</Version>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>