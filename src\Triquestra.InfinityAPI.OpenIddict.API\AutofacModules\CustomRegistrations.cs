﻿using Autofac;
using Microsoft.Data.SqlClient;
using System.Data;
using Triquestra.Common;
using Triquestra.Common.Logging;
using Triquestra.Common.Services;
using Triquestra.InfinityAPI.Common.API.Infrastructure.AutofacModules;
using Triquestra.InfinityAPI.Infrastructure.RequestValidation.Core;
using Triquestra.InfinityAPI.Infrastructure.RequestValidation.Idempotency;
using Triquestra.InfinityAPI.Infrastructure.RequestValidation.Repositories;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;
using Triquestra.InfinityAPI.OpenIddict.Infrastructure;
using Triquestra.InfinityAPI.OpenIddict.Infrastructure.Repositories;

namespace Triquestra.InfinityAPI.OpenIddictApi.AutofacModules
{
    public static class CustomRegistrations
    {
        public static void RegisterCustomClasses(ContainerRegistrator builder, string connString)
        {

            builder.Builder.Register(c => new Configuration(OpenIddictDbContext.SCHEMA))
                .As<IConfiguration>()
                .SingleInstance();

            builder.Builder.Register<CommonDbContext>(c => c.Resolve<OpenIddictDbContext>())
          .InstancePerLifetimeScope();

            builder.Builder.RegisterType<ClientRequestRepository>()
            .As<IClientRequestRepository>()
                    .InstancePerLifetimeScope();
            builder.Builder.RegisterType<RequestManager>()
                    .As<IRequestManager>()
                    .InstancePerLifetimeScope();

            builder.Builder.RegisterType<ClientRepository>()
                .As<IClientRepository>()
                .InstancePerLifetimeScope();

            builder.Builder.RegisterType<JsonSerializeService>()
            .As<IJsonSerializeService>()
            .InstancePerLifetimeScope();
        }
    }
}
