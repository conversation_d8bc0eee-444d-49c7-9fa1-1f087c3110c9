﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.36105.23
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{B4C9F112-7435-4A84-98DC-7411175FFD15}"
	ProjectSection(SolutionItems) = preProject
		configuration.json = configuration.json
		README.md = README.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Triquestra.InfinityAPI.OpenIddictApi", "src\Triquestra.InfinityAPI.OpenIddict.API\Triquestra.InfinityAPI.OpenIddictApi.csproj", "{2C558E30-A58B-D6C4-6E57-1ECD4AFA7836}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Triquestra.InfinityAPI.OpenIddict.Domain", "src\Triquestra.InfinityAPI.OpenIddict.Domain\Triquestra.InfinityAPI.OpenIddict.Domain.csproj", "{3C7F8839-088E-40BD-835C-059EE7056958}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Triquestra.InfinityAPI.OpenIddict.Infrastructure", "src\Triquestra.InfinityAPI.OpenIddict.Infrastructure\Triquestra.InfinityAPI.OpenIddict.Infrastructure.csproj", "{FE334078-A52C-4AE0-B53B-0042919D1C81}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Triquestra.InfinityAPI.OpenIddict.Application", "src\Triquestra.InfinityAPI.OpenIddict.Application\Triquestra.InfinityAPI.OpenIddict.Application.csproj", "{50B24EA4-5668-461B-9B8C-DF3776864CF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OpenIddict_MSSQL_Identity", "src\DB\OpenIddict_MSSQL_Identity.csproj", "{6E7EC732-FF9A-17D8-386B-BB7EF4E07B38}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2C558E30-A58B-D6C4-6E57-1ECD4AFA7836}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C558E30-A58B-D6C4-6E57-1ECD4AFA7836}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C558E30-A58B-D6C4-6E57-1ECD4AFA7836}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C558E30-A58B-D6C4-6E57-1ECD4AFA7836}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C7F8839-088E-40BD-835C-059EE7056958}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C7F8839-088E-40BD-835C-059EE7056958}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C7F8839-088E-40BD-835C-059EE7056958}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C7F8839-088E-40BD-835C-059EE7056958}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE334078-A52C-4AE0-B53B-0042919D1C81}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE334078-A52C-4AE0-B53B-0042919D1C81}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE334078-A52C-4AE0-B53B-0042919D1C81}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE334078-A52C-4AE0-B53B-0042919D1C81}.Release|Any CPU.Build.0 = Release|Any CPU
		{50B24EA4-5668-461B-9B8C-DF3776864CF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50B24EA4-5668-461B-9B8C-DF3776864CF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50B24EA4-5668-461B-9B8C-DF3776864CF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50B24EA4-5668-461B-9B8C-DF3776864CF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E7EC732-FF9A-17D8-386B-BB7EF4E07B38}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E7EC732-FF9A-17D8-386B-BB7EF4E07B38}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E7EC732-FF9A-17D8-386B-BB7EF4E07B38}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E7EC732-FF9A-17D8-386B-BB7EF4E07B38}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2C558E30-A58B-D6C4-6E57-1ECD4AFA7836} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3C7F8839-088E-40BD-835C-059EE7056958} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{FE334078-A52C-4AE0-B53B-0042919D1C81} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{50B24EA4-5668-461B-9B8C-DF3776864CF8} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6E7EC732-FF9A-17D8-386B-BB7EF4E07B38} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F3B7F505-7CDB-465E-AE0D-6DB41EFDDB76}
	EndGlobalSection
EndGlobal
