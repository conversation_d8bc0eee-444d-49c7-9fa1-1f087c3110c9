﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Triquestra.Common.Domain.Core;
using Triquestra.Common.Logging;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;

namespace Triquestra.InfinityAPI.OpenIddict.Infrastructure.Repositories
{
    public class ClientRepository : IClientRepository
    {
        private readonly OpenIddictDbContext _context;
        private readonly IInfinityLogger _logger;
        public IUnitOfWork UnitOfWork
        {
            get
            {
                return _context;
            }
        }

        public ClientRepository(OpenIddictDbContext context, IInfinityLoggerFactory loggerFactory)
        {
            _context = context;
            _logger = loggerFactory.CreateLogger<ClientRepository>();
        }

        public Client GetClientById(int id)
        {
            return _context.Clients.Include(x => x.Scopes)
                                   .Include(x => x.Claims)
                                   .FirstOrDefault(x => x.Id == id);
        }

        public Client GetClientByClientId(string clientId)
        {
            return _context.Clients.Include(x => x.Scopes)
                                   .Include(x => x.Claims)
                                   .FirstOrDefault(x => x.ClientId==clientId);
        }

        public void Add(Client client)
        {
            _context.Clients.Add(client);
        }
    }
}
