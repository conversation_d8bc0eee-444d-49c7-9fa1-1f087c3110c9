﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using Triquestra.Common.Domain.Core;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;

namespace Triquestra.InfinityAPI.OpenIddict.Infrastructure.Repositories
{
    public class ClientRepository : IClientRepository
    {
        private readonly OpenIddictDbContext _context;
        private readonly ILogger<ClientRepository> _logger;
        public IUnitOfWork UnitOfWork
        {
            get
            {
                return _context;
            }
        }

        public ClientRepository(OpenIddictDbContext context, ILogger<ClientRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public Client GetClientById(int id)
        {
            return _context.Clients.Include(x => x.Scopes)
                                   .Include(x => x.Claims)
                                   .FirstOrDefault(x => x.Id == id);
        }

        public Client GetClientByClientId(string clientId)
        {
            return _context.Clients.Include(x => x.Scopes)
                                   .Include(x => x.Claims)
                                   .FirstOrDefault(x => x.ClientId==clientId);
        }

        public void Add(Client client)
        {
            _context.Clients.Add(client);
        }
    }
}
