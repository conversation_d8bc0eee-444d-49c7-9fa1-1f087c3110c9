﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Debug"
      internalLogFile="c:\temp\internal.txt">


  <extensions>
    <!--enable NLog.Web for ASP.NET Core-->
    <add assembly="NLog.Web.AspNetCore"/>
    <add assembly="NLog.RabbitMQ.Target" />
    <add assembly="NLog.Extensions.AzureServiceBus" />
  </extensions>

  <!-- define various log targets -->
  <targets async="true">
    <!-- write logs to file -->
    <target xsi:type="File" name="allfile" fileName="c:\Nlogs\openiddict-${shortdate}.log">
      <layout xsi:type="JsonLayout">
        <attribute name="time" layout="${date:universalTime=True:format=yyyy-MM-ddTHH\:mm\:ss.fffZ}" />
        <attribute name="level" layout="${level:upperCase=true}"/>
        <attribute name="logging-id" layout="${aspnet-request:header=x-logging-id}" />
        <attribute name="request-id" layout="${aspnet-request:header=x-request-id}" />       
		<attribute name="user-agent" layout="${aspnet-request-useragent}" />
        <attribute name="exception" layout="${exception}" />
        <attribute name="message" layout="${message}" />
        <attribute name="class-name" layout="${logger}"/>
        <attribute name="machinename" layout="${machinename}" />
        <attribute name="request-verb" layout="${aspnet-Request-Method}" />
        <attribute name="request-path" layout="${aspnet-request-url:IncludeHost=false}" />
        <attribute name="api-name" layout="Identity6" />
        <attribute name="api-version" layout="1.0.0" />
        <attribute name="host-port" layout="${inf-appsettings:DefaultPort}" />
        <attribute name="base-dir" layout="${basedir}" />
        <attribute name="client-ip-address" layout="${aspnet-request-ip}" />
        <attribute name="response-code" layout="${aspnet-item:variable=response-code}" />
        <attribute name="response-message" layout="${aspnet-item:variable=response-message}" />
      </layout>
    </target>

    
  
    <target xsi:type="Null" name="blackhole" />
  </targets>

  <rules>
    <!--All logs, including from Microsoft-->
    <logger name="*" minlevel="Trace" writeTo="allfile" />
  </rules>
</nlog>
