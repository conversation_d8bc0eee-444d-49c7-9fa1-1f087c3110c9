﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Warn"
      internalLogFile="c:\NLogs\MyApp\nlog.internal.txt">

  <extensions>
    <add assembly="NLog.Targets.RabbitMQCore" />
    <add assembly="ConsoleApp2" />
    <add assembly="InfinityAPI.DBManager" />
  </extensions>
  <targets async="true">
    <!-- write logs to file -->
    <target xsi:type="File" name="allfile" fileName="c:\Nlogs\InfinityApiDBManager-${date:format=yyyyMMdd}.log" archiveEvery="Day" maxArchiveFiles="7">
      <layout xsi:type="JsonLayout">
        <attribute name="time" layout="${date:universalTime=True:format=yyyy-MM-ddTHH\:mm\:ss.fffZ}" />
        <attribute name="level" layout="${level:upperCase=true}"/>
        <attribute name="message" layout="${message}" />
        <attribute name="class-name" layout="${logger}"/>
        <attribute name="machinename" layout="${machinename}" />
        <attribute name="api-name" layout="${inf-apiname}" />
        <attribute name="api-version" layout="1.0.1" />
      </layout>
    </target>

    <target xsi:type="File" name="ownFile" fileName="c:\Nlogs\nlog-own-${shortdate}.log"
             layout="${longdate}|${logger}|${uppercase:${level}}|${message} ${exception}" />


    <target xsi:type="Null" name="blackhole" />
  </targets>

  <rules>

    <logger name="*" minlevel="Info" writeTo="allfile" />
    <logger name="Triquestra.*" minlevel="Info" writeTo="File" />
    <logger name="Microsoft.*" minlevel="Trace" writeTo="blackhole" final="true" />
  </rules>
</nlog>