﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Triquestra.Common.Logging" Version="8.0.0.2" />
    <PackageReference Include="Triquestra.InfinityApi.Common.Infrastructure" Version="8.0.0.2" />
    <PackageReference Include="Triquestra.InfinityAPI.Infrastructure.RequestValidation" Version="8.0.0.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InfinityAPI.OpenIddict.Domain\Triquestra.InfinityAPI.OpenIddict.Domain.csproj" />
  </ItemGroup>
</Project>
