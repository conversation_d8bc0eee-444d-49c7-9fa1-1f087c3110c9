﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <Description>Triquestra.InfinityAPI.OpenIddict.API</Description>
    <Copyright>Triquestra.InfinityAPI.OpenIddict.API</Copyright>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <AssemblyName>Triquestra.InfinityAPI.OpenIddict.API</AssemblyName>
    <PackageId>Triquestra.InfinityAPI.OpenIddict.API</PackageId>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="..\..\configuration.json" Link="configuration.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NETStandard.Library" Version="2.0.3" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.ApiDescription.Server" Version="9.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Nlog.RabbitMQ.Target" Version="2.11.20" />
    <PackageReference Include="NLog.Targets.ElasticSearch" Version="7.7.0" />
    <PackageReference Include="OpenIddict.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.3.1" />
    <PackageReference Include="Triquestra.InfinityAPI.Common.API" Version="*******" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InfinityAPI.OpenIddict.Application\Triquestra.InfinityAPI.OpenIddict.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="encryption.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="sign.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="tq_encryption.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="tq_sign.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
