{"SlnFileName": "Triquestra.InfinityAPI.OpenIddict.API.sln", "Configuration": "Release", "Verbosity": "Normal", "Major": "1", "Minor": "0", "Patch": "0", "Build": "0", "BuildOffset": "", "PackageSuffix": "-rc1", "PackageName": "OpenIddictApiPkg", "OctopusProjectName": "OpenIddictApi", "CreateRelease": "yes", "DeployEnvironment": "QA", "SwaggerVersion": "1.0.0", "BaseURL": "/Triquestra/openiddict/", "Projects": [{"ProjectName": "Triquestra.InfinityAPI.OpenIddictApi.csproj", "ProjectPath": "\\src\\Triquestra.InfinityAPI.OpenIddict.API\\", "ProjectType": "Main"}, {"ProjectName": "OpenIddict_MSSQL_Identity.csproj", "ProjectPath": "\\src\\DB\\", "ProjectType": "DB"}], "TestProjects": []}