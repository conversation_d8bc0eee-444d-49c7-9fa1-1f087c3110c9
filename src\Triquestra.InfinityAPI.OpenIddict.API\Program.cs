
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using NLog.Web;
using System.Globalization;
using System.IO;


namespace Triquestra.InfinityAPI.OpenIddictApi
{
    public class Program
    {

        public static void Main(string[] args)
        {
            var hostBuilder = new WebHostBuilder();
            var environment = hostBuilder.GetSetting("environment");
            var builder = new ConfigurationBuilder()
               .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{environment}.json", optional: true)
                .AddEnvironmentVariables();

            var configuration = builder.Build();

            var port = GetPortsFomCommandArgs(args);
            if (string.IsNullOrEmpty(port))
            {
                port = configuration["DefaultPort"];
            }
            var host = new WebHostBuilder()
                 .UseKestrel()
                 .UseContentRoot(Directory.GetCurrentDirectory())
                 .UseIISIntegration()
                 .UseStartup<Startup>()
                 .UseUrls($"http://*:{port}")
                 .UseNLog()
                 .Build();

            host.Run();
        }

        private static string GetPortsFomCommandArgs(string[] args)
        {
            if (args == null || args.Length == 0)
                return null;

            for (var i = 0; i < args.Length; i++)
            {
                var argument = args[i].ToLower(CultureInfo.CurrentCulture);

                // parse port number
                if (argument == "-p" || argument == "--port")
                {
                    if (args.Length <= (i + 1))
                        return null;

                    if (!int.TryParse(args[i + 1], out int value))
                        return null;

                    return value.ToString();
                }
            }

            return null;
        }
    }
}
