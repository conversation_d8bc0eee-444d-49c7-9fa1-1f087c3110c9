﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using OpenIddict.EntityFrameworkCore.Models;
using Triquestra.Common;
using Triquestra.InfinityAPI.Infrastructure.RequestValidation.Core;
using Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict;

namespace Triquestra.InfinityAPI.OpenIddict.Infrastructure
{

    public class OpenIddictDbContext : CommonDbContext
    {
        public const string SCHEMA = "OpenIddict";
        public DbSet<Client> Clients { get; set; }
        public DbSet<ClientClaim> ClientClaims { get; set; }
        public DbSet<ClientScope> ClientScopes { get; set; }
        public OpenIddictDbContext(DbContextOptions<OpenIddictDbContext> options, IMediator mediator, IConfiguration cfg)
        : base(options, mediator, cfg)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            
           

            builder.Entity<Client>(b =>
            {
                b.ToTable("OpenIddictApplications", SCHEMA);
                b.<PERSON>(c => c.Id);
                b.Property(c => c.Id).ValueGeneratedOnAdd();
            });

            builder.Entity<ApplicationToken>(b =>
            {
                b.ToTable("OpenIddictTokens", SCHEMA);
                b.HasKey(c => c.Id);
                b.Property(c => c.Id).ValueGeneratedOnAdd();
            });

            builder.Entity<ApplicationAuthorization>(b =>
            {
                b.ToTable("OpenIddictAuthorizations", SCHEMA);
                b.HasKey(c => c.Id);
                b.Property(c => c.Id).ValueGeneratedOnAdd();
            });

            builder.Entity<ApplicationScope>(b =>
            {
                b.ToTable("OpenIddictScopes", SCHEMA);
                b.HasKey(c => c.Id);
                b.Property(c => c.Id).ValueGeneratedOnAdd();
            });

            builder.Entity<ClientClaim>(b =>
            {
                b.ToTable("ClientClaims", SCHEMA);
                b.HasKey(cc => cc.Id);
                b.Property(cc => cc.Id).ValueGeneratedOnAdd();
                b.HasOne(x=>x.Client).WithMany(c => c.Claims).HasForeignKey(x => x.ClientId);
            });

            builder.Entity<ClientScope>(b =>
            {
                b.ToTable("ClientScopes", SCHEMA);
                b.HasKey(cs => cs.Id);
                b.Property(cs => cs.Id).ValueGeneratedOnAdd();
                b.HasOne(x => x.Client).WithMany(c => c.Scopes).HasForeignKey(x => x.ClientId);
            });

            base.OnModelCreating(builder);
        }
    }
}
