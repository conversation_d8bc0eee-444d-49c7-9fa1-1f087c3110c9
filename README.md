# Triquestra.InfinityAPI.OpenIddict.API

This is an auth API built with OpenIddict


## Database

1. OpenIddict.OpenIddictApplications is used for storing Clients
1. OpenIddict.ClientClaims is used for storing client claims
1. OpenIddict.ClientScopes is use for storing client scopes

#### run the db manager to upgrade database

``` powershell
.\InfinityAPI.DBManager.exe
```

## certifications

the Old identity server is only requires 1 certificate, but OpenIddict requires 2 certificates:
1. the signing certificate - sign.pfx
1. the encryption certificate - encrypt.pfx

the certificate password is read from the environment variable `INFINITY_IDENTITY_PASSWORD` same as the old identity server.
the 2 certificate are currently sharing the same password `tq`

## default clients

#### when the API starts, it will create the following clients for testing as there're no admin UI at this time:

1. rw  with ReadWrite scope
1. ro with ReadOnly scope

#### All the clients have claims
1. terminalId=1
1. site_code=99

## Request for access token

#### the openiddict does not allow the request body to be application/json, it requires the request body to be application/x-www-form-urlencoded

sample request for the token:
``` bash
curl --location 'http://192.168.126.100:5000/connect/token' --header 'Content-Type: application/x-www-form-urlencoded' --data-urlencode 'client_id=rw' --data-urlencode 'client_secret=p@ssword' --data-urlencode 'grant_type=client_credentials'
```

would response:

```json
{
  "access_token": "...",
  "token_type": "Bearer",
  "expires_in": 28800
}

```

jwt payload sample:

``` json
{
  "iss": "http://192.168.126.100:5000/",
  "exp": 1755600945,
  "iat": 1755572145,
  "jti": "47e3d32a-c745-476f-9252-6d4656e24638",
  "sub": "rw",
  "scope": "ReadWrite",
  "site_code": "99",
  "terminalId": "1",
  "oi_prst": "rw",
  "client_id": "rw"
}

```

