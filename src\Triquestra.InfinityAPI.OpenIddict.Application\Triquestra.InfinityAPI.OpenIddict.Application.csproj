﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="APIIntegrationInterfaces.Transactions.Transfers" Version="*******" />
    <PackageReference Include="Autofac" Version="8.2.0" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.3.1" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
    <PackageReference Include="Triquestra.Common.Domain" Version="*******" />
    <PackageReference Include="Triquestra.InfinityAPI.ProductInventory.Models" Version="*******" />
    <PackageReference Include="Triquestra.InfinityAPI.Common.Application" Version="*******" />
    <PackageReference Include="Triquestra.InfinityAPI.Infrastructure.RequestValidation" Version="*******" />
    <PackageReference Include="Triquestra.InfinityAPI.Transactions.StockReceipt.Models" Version="*******" />
    <PackageReference Include="Triquestra.InfinityAPI.Transactions.Transfers.Model" Version="*******" />
    <PackageReference Include="APIIntegrationInterfaces.Transactions.StockReceipt" Version="*******" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InfinityAPI.OpenIddict.Infrastructure\Triquestra.InfinityAPI.OpenIddict.Infrastructure.csproj" />
  </ItemGroup>
</Project>
