﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="4.6.1" targetFramework="net461" />
  <package id="Autofac.Extensions.DependencyInjection" version="4.2.0" targetFramework="net461" />
  <package id="dbup" version="4.0.0-beta0002" targetFramework="net461" />
  <package id="dbup-core" version="4.0.0-beta0002" targetFramework="net461" />
  <package id="dbup-sqlserver" version="4.0.0-beta0002" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Http" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.Routing.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.DependencyInjection" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Options" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.Extensions.PlatformAbstractions" version="1.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Primitives" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Net.Http.Headers" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.NETCore.Platforms" version="1.0.1" targetFramework="net461" />
  <package id="Microsoft.Win32.Primitives" version="4.0.1" targetFramework="net461" />
  <package id="NETStandard.Library" version="1.6.0" targetFramework="net461" />
  <package id="NLog" version="4.5.0" targetFramework="net461" />
  <package id="NLog.Extensions.Logging" version="1.0.0-rtm-beta7" targetFramework="net461" />
  <package id="NLog.Web.AspNetCore" version="4.5.0-beta03" targetFramework="net461" />
  <package id="System.AppContext" version="4.1.0" targetFramework="net461" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net461" />
  <package id="System.Collections" version="4.0.11" targetFramework="net461" />
  <package id="System.Collections.Concurrent" version="4.0.12" targetFramework="net461" />
  <package id="System.Console" version="4.0.0" targetFramework="net461" />
  <package id="System.Data.SqlClient" version="4.4.3" targetFramework="net461" />
  <package id="System.Diagnostics.Debug" version="4.0.11" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.0.0" targetFramework="net461" />
  <package id="System.Diagnostics.Tools" version="4.0.1" targetFramework="net461" />
  <package id="System.Diagnostics.Tracing" version="4.1.0" targetFramework="net461" />
  <package id="System.Globalization" version="4.0.11" targetFramework="net461" />
  <package id="System.Globalization.Calendars" version="4.0.1" targetFramework="net461" />
  <package id="System.IO" version="4.1.0" targetFramework="net461" />
  <package id="System.IO.Compression" version="4.1.0" targetFramework="net461" />
  <package id="System.IO.Compression.ZipFile" version="4.0.1" targetFramework="net461" />
  <package id="System.IO.FileSystem" version="4.0.1" targetFramework="net461" />
  <package id="System.IO.FileSystem.Primitives" version="4.0.1" targetFramework="net461" />
  <package id="System.Linq" version="4.1.0" targetFramework="net461" />
  <package id="System.Linq.Expressions" version="4.1.0" targetFramework="net461" />
  <package id="System.Net.Http" version="4.1.0" targetFramework="net461" />
  <package id="System.Net.Primitives" version="4.0.11" targetFramework="net461" />
  <package id="System.Net.Sockets" version="4.1.0" targetFramework="net461" />
  <package id="System.ObjectModel" version="4.0.12" targetFramework="net461" />
  <package id="System.Reflection" version="4.1.0" targetFramework="net461" />
  <package id="System.Reflection.Extensions" version="4.0.1" targetFramework="net461" />
  <package id="System.Reflection.Primitives" version="4.0.1" targetFramework="net461" />
  <package id="System.Resources.ResourceManager" version="4.0.1" targetFramework="net461" />
  <package id="System.Runtime" version="4.1.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.4.0" targetFramework="net461" />
  <package id="System.Runtime.Extensions" version="4.1.0" targetFramework="net461" />
  <package id="System.Runtime.Handles" version="4.0.1" targetFramework="net461" />
  <package id="System.Runtime.InteropServices" version="4.1.0" targetFramework="net461" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.0.0" targetFramework="net461" />
  <package id="System.Runtime.Numerics" version="4.0.1" targetFramework="net461" />
  <package id="System.Security.Cryptography.Algorithms" version="4.2.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Encoding" version="4.0.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Primitives" version="4.0.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.1.0" targetFramework="net461" />
  <package id="System.Text.Encoding" version="4.0.11" targetFramework="net461" />
  <package id="System.Text.Encoding.Extensions" version="4.0.11" targetFramework="net461" />
  <package id="System.Text.Encodings.Web" version="4.4.0" targetFramework="net461" />
  <package id="System.Text.RegularExpressions" version="4.1.0" targetFramework="net461" />
  <package id="System.Threading" version="4.0.11" targetFramework="net461" />
  <package id="System.Threading.Tasks" version="4.0.11" targetFramework="net461" />
  <package id="System.Threading.Timer" version="4.0.1" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.3.0" targetFramework="net461" />
  <package id="System.Xml.ReaderWriter" version="4.0.11" targetFramework="net461" />
  <package id="System.Xml.XDocument" version="4.0.11" targetFramework="net461" />
  <package id="Triquestra.InfinityAPI.Common.Logging" version="1.0.0" targetFramework="net461" />
  <package id="InfinityAPI.DBManager" version="1.0.0" targetFramework="net461" />
</packages>