﻿using OpenIddict.EntityFrameworkCore.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Triquestra.InfinityAPI.OpenIddict.Domain.AggregatesModel.OpenIddict
{
    public class Client : OpenIddictEntityFrameworkCoreApplication<int, ApplicationAuthorization, ApplicationToken>
    {
        public List<ClientScope> Scopes { get; set; }
        public List<ClientClaim> Claims { get; set; }
    }
}
